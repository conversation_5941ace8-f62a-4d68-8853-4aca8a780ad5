#!/usr/bin/env python
# coding: utf-8
# Final Journal Testing Tool - Optimized Terminal Version
# All 15 tests with exact logic from original file.py

import warnings
warnings.filterwarnings('ignore')
import pandas as pd
import numpy as np
import re
import os
import datetime
import calendar

pd.options.display.float_format = '{:,.2f}'.format

# Global variables
rev_code = []
bank_acc = []
pre_acc = []
accrual_acc = []
pl_acc = []
account_codes = ""
doc_no = ""
date = ""
amount = ""
acc_description = ""
acc_type = ""
time = ""
post_by = ""
date_format = ""
time_format = ""
client_name = ""
client_period = ""
holiday_dates = []
df = None
dataset = None
results = {}

def load_configuration():
    """Load configuration from SOP file - exact logic from original"""
    global rev_code, bank_acc, pre_acc, accrual_acc, pl_acc
    global account_codes, doc_no, date, amount, acc_description
    global acc_type, time, post_by, date_format, time_format
    global client_name, client_period, holiday_dates
    
    try:
        # Load account codes - exact same logic
        rev_code = list(pd.read_excel("SOP for Data Analyst.xlsx", sheet_name="Revenue Code")["Revenue Code"].astype(str))
        bank_acc = list(pd.read_excel("SOP for Data Analyst.xlsx", sheet_name="Bank Code")["Bank Code"].astype(str))
        pre_acc = list(pd.read_excel("SOP for Data Analyst.xlsx", sheet_name="Prepayment Code")["Prepayment Code"].astype(str))
        accrual_acc = list(pd.read_excel("SOP for Data Analyst.xlsx", sheet_name="Accrual Code")["Accrual Code"].astype(str))
        pl_acc = list(pd.read_excel("SOP for Data Analyst.xlsx", sheet_name="PL Code")["PL Code"].astype(str))
        
        # Load column names - exact same logic
        col_name = pd.read_excel("SOP for Data Analyst.xlsx", sheet_name="Col Name").iloc[:,1]
        account_codes = col_name[0]
        doc_no = col_name[1]
        date = col_name[2]
        amount = col_name[3]
        acc_description = col_name[4]
        acc_type = col_name[5]
        time = col_name[6]
        post_by = col_name[7]
        date_format = col_name[8]
        time_format = col_name[9]
        
        # Load client details - exact same logic
        client_detail = pd.read_excel("SOP for Data Analyst.xlsx", sheet_name="Journal Testing", header=None)
        client_name = client_detail.iloc[1,0]
        client_period = "Period : " + client_detail.iloc[8,1].strftime("%B %Y") + " To " + client_detail.iloc[8,2].strftime("%B %Y")
        
        # Load holiday dates - exact same logic
        holiday_detail = pd.read_excel("SOP for Data Analyst.xlsx", sheet_name="Journal Testing", header=None)
        index = holiday_detail[holiday_detail[0].str.strip() == "Date"].index
        
        hol_dates = []
        for i in holiday_detail.iloc[index[0]+1:,0].values:
            if pd.isna(i):
                break
            else:
                hol_dates.append(i)
        
        holiday_dates = []
        for i in hol_dates:
            holiday_dates.append(i.strftime("%Y-%m-%d"))
        
        print("Configuration loaded successfully!")
        return True
        
    except Exception as e:
        print(f"Error loading configuration: {e}")
        return False

def print_summary():
    """Print summary of tests to be performed - exact from original"""
    print("\n" + "="*60)
    print("JOURNAL TESTING SUMMARY")
    print("="*60)
    print(f"Client: {client_name}")
    print(f"Period: {client_period}")
    print("Subject: Journal Testing")
    print("\nTests to be performed:")
    print("1.  Round entries ,000 or ,999")
    print("2.  Date of postings: weekends, bank holidays etc.")
    print("3.  Timings of postings - any postings on odd hours")
    print("4.  Total amount of transactions per month")
    print("5.  Reversed Journal Entries")
    print("6.  Gaps/jumps in Journal Entry numbers")
    print("7.  Summary of Debit transactions in Revenue codes")
    print("8.  Prepayments vs Bank")
    print("9.  Accruals vs Bank")
    print("10. Bank accounts vs PnL accounts")
    print("11. Postings by directors on Companies house")
    print("12. Possible duplicate Journal entries")
    print("13. Fraud Word Check")
    print("14. Sales Chronological Testing")
    print("15. Credits in Revenue")
    print("="*60)

def round_entries(Amount):
    """Test 1: Find round entries ending with 000 or 999 - exact logic from original"""
    global amount, dataset, results

    try:
        print("\nTest 1: Round entries ,000 or ,999")
        print("-" * 40)

        if amount != "na":
            entries = dataset[dataset[amount].notnull()]
            entries[amount] = entries[amount].astype(str).str.strip().str.replace(",","")
            entries = entries[entries[amount] != ""]
            entries_000 = entries[(entries[amount].astype(str).astype(float).astype(str).str.contains(r"0{3}\.0*$"))]
            entries_000[amount] = pd.to_numeric(entries_000[amount])
            entries_000 = entries_000.sort_values(amount, ascending=False)
            entries_999 = entries[(entries[amount].astype(str).astype(float).astype(str).str.contains(r"9{3}\.0*$"))]
            entries_999[amount] = pd.to_numeric(entries_999[amount])
            entries_999 = entries_999.sort_values(amount, ascending=False)
            round_entries_result = pd.concat([entries_000,entries_999], ignore_index=True)

            print(f"Entries ending with '000': {len(entries_000)}")
            print(f"Entries ending with '999': {len(entries_999)}")

            if len(entries_000) > 0:
                print("\nTop 5 entries ending with '000':")
                print(entries_000.head().to_string(index=False))

            if len(entries_999) > 0:
                print("\nTop 5 entries ending with '999':")
                print(entries_999.head().to_string(index=False))

            # Conclusion - exact logic from original
            if len(entries_000) > 0 and len(entries_999) > 0:
                conclusion = "Entries ending with '000' & '999' found."
            elif len(entries_000) > 0 and len(entries_999) <= 0:
                conclusion = "Entries ending with '000' found."
            elif len(entries_000) <= 0 and len(entries_999) > 0:
                conclusion = "Entries ending with '999' found."
            else:
                conclusion = "No Entries ending with '000' & '999' found."

            print(f"\nConclusion: {conclusion}")
            results['test_1'] = {'conclusion': conclusion, 'count_000': len(entries_000), 'count_999': len(entries_999)}
        else:
            print("Amount column name not provided")
            results['test_1'] = {'conclusion': 'Col Name Not Given', 'count_000': 0, 'count_999': 0}

    except Exception as e:
        print(f"Error in round_entries: {e}")
        results['test_1'] = {'conclusion': 'Error occurred', 'count_000': 0, 'count_999': 0}

def holidaysandweekend(Date, f):
    """Test 2: Find postings on weekends and holidays - exact logic from original"""
    global date, amount, dataset, holiday_dates, results

    try:
        print("\nTest 2: Date of postings: weekends, bank holidays etc.")
        print("-" * 50)

        if date != "na" and f != "na":
            date = date.strip()
            df = dataset.copy()
            df[date] = df[date].astype(str).str.strip()
            df = df[df[date].notnull()]
            df = df[df[date] != ""]
            df[date] = pd.to_datetime(df[date], format=f)

            holidays = holiday_dates
            holiday = df[df[date].isin(holidays)]
            weekend_1 = df[df[date].dt.strftime("%A") == "Saturday"]
            weekend_2 = df[df[date].dt.strftime("%A") == "Sunday"]

            holiday["Holiday"] = "Holiday"
            weekend_1["Holiday"] = "Saturday"
            weekend_2["Holiday"] = "Sunday"

            holiday = holiday.sort_values(amount, ascending=False)
            weekend_1 = weekend_1.sort_values(amount, ascending=False)
            weekend_2 = weekend_2.sort_values(amount, ascending=False)
            weekend = pd.concat([weekend_1, weekend_2], ignore_index=True)

            holidays_trans = pd.concat([holiday, weekend_1, weekend_2], ignore_index=True)
            holidays_trans = holidays_trans.drop_duplicates()
            holiday = holidays_trans[holidays_trans["Holiday"] == "Holiday"]
            weekend_1 = holidays_trans[holidays_trans["Holiday"] == "Saturday"]
            weekend_2 = holidays_trans[holidays_trans["Holiday"] == "Sunday"]

            print(f"Weekend entries: {len(weekend)}")
            print(f"Holiday entries: {len(holiday)}")

            if len(weekend) > 0:
                print("\nTop 5 weekend entries:")
                print(weekend.head().to_string(index=False))

            if len(holiday) > 0:
                print("\nTop 5 holiday entries:")
                print(holiday.head().to_string(index=False))

            # Conclusion - exact logic from original
            if len(weekend) > 0 and len(holiday) > 0:
                conclusion = "Entries posted in 'Weekend' & 'Holiday' found."
            elif len(weekend) > 0 and len(holiday) <= 0:
                conclusion = "Entries posted in 'Weekend' found."
            elif len(weekend) <= 0 and len(holiday) > 0:
                conclusion = "Entries posted in 'Holiday' found."
            else:
                conclusion = "No Entries posted in 'Weekend' & 'Holiday' found."

            print(f"\nConclusion: {conclusion}")
            results['test_2'] = {'conclusion': conclusion, 'weekend_count': len(weekend), 'holiday_count': len(holiday)}

        else:
            print("Date column name or format not provided")
            results['test_2'] = {'conclusion': 'Col Name Not Given', 'weekend_count': 0, 'holiday_count': 0}

    except Exception as e:
        print(f"Error in holidaysandweekend: {e}")
        results['test_2'] = {'conclusion': 'Error occurred', 'weekend_count': 0, 'holiday_count': 0}

def odd_hours_entries(Time, f):
    """Test 3: Odd hours entries - exact logic from original"""
    global time, amount, dataset, results

    try:
        print("\nTest 3: Timings of postings - any postings on odd hours")
        print("-" * 55)

        if time != "na" and f != "na":
            time = time.strip()
            df = dataset.copy()
            df[time] = df[time].astype(str).str.strip()
            df = df[df[time].notnull()]
            df = df[df[time] != ""]
            df[time] = pd.to_datetime(df[time], format=f)

            # Exact same hour filtering logic from original
            pm_8 = df[df[time].dt.hour == 20]
            pm_9 = df[df[time].dt.hour == 21]
            pm_10 = df[df[time].dt.hour == 22]
            pm_11 = df[df[time].dt.hour == 23]
            am_12 = df[df[time].dt.hour == 0]
            am_1 = df[df[time].dt.hour == 1]
            am_2 = df[df[time].dt.hour == 2]
            am_3 = df[df[time].dt.hour == 3]
            am_4 = df[df[time].dt.hour == 4]
            am_5 = df[df[time].dt.hour == 5]
            am_6 = df[df[time].dt.hour == 6]
            am_7 = df[df[time].dt.hour == 7]
            am_8 = df[df[time].dt.hour == 8]

            # Add hour labels - exact same logic from original
            pm_8["Hours"] = "8 PM"
            pm_9["Hours"] = "9 PM"
            pm_10["Hours"] = "10 PM"
            pm_11["Hours"] = "11 PM"
            am_12["Hours"] = "12 AM"
            am_1["Hours"] = "1 AM"
            am_2["Hours"] = "2 AM"
            am_3["Hours"] = "3 AM"
            am_4["Hours"] = "4 AM"
            am_5["Hours"] = "5 AM"
            am_6["Hours"] = "6 AM"
            am_7["Hours"] = "7 AM"
            am_8["Hours"] = "8 AM"

            # Concatenate and sort - exact same logic from original
            odd_hours_am = pd.concat([am_12,am_1,am_2,am_3,am_4,am_5,am_6,am_7,am_8], ignore_index=True)
            odd_hours_pm = pd.concat([pm_8,pm_9,pm_10,pm_11], ignore_index=True)
            odd_hours_am = odd_hours_am.sort_values(amount, ascending=False)
            odd_hours_pm = odd_hours_pm.sort_values(amount, ascending=False)

            odd_hours_concat = pd.concat([pm_8,pm_9,pm_10,pm_11,am_12,am_1,am_2,am_3,am_4,am_5,am_6,am_7,am_8], ignore_index=True)

            print(f"Total odd hours entries: {len(odd_hours_concat)}")
            print(f"AM entries (12AM-8AM): {len(odd_hours_am)}")
            print(f"PM entries (8PM-11PM): {len(odd_hours_pm)}")

            if len(odd_hours_am) > 0:
                print("\nTop 5 AM odd hours entries:")
                print(odd_hours_am.head().to_string(index=False))

            if len(odd_hours_pm) > 0:
                print("\nTop 5 PM odd hours entries:")
                print(odd_hours_pm.head().to_string(index=False))

            # Conclusion - exact same logic from original
            if len(odd_hours_am) > 0 or len(odd_hours_pm) > 0:
                conclusion = "Entries posted in 'Odd hours' found."
            else:
                conclusion = "No Entries posted in 'Odd hours' found."

            print(f"\nConclusion: {conclusion}")
            results['test_3'] = {'conclusion': conclusion, 'odd_hours_count': len(odd_hours_concat)}

        else:
            print("Time column name or format not provided")
            results['test_3'] = {'conclusion': 'Col Name Not Given', 'odd_hours_count': 0}

    except Exception as e:
        print(f"Error in odd_hours_entries: {e}")
        results['test_3'] = {'conclusion': 'Error occurred', 'odd_hours_count': 0}

def transactions_per_month(Date, f):
    """Test 4: Monthly transactions - exact logic from original"""
    global date, amount, dataset, results

    try:
        print("\nTest 4: Total amount of transactions per month")
        print("-" * 45)

        if date != "na" and f != "na":
            date_col = date.strip()
            df = dataset.copy()
            df.sort_values(by=date_col, inplace=True)

            df[date_col] = df[date_col].astype(str).str.strip()
            df = df[df[date_col].notnull()]
            df = df[df[date_col] != ""]
            df[date_col] = pd.to_datetime(df[date_col], format=f)
            df["month"] = df[date_col].dt.strftime("%m")

            df = df[(df["month"].notnull()) & (df["month"] != "nan")]
            debit_data = df[df[amount] > 0]

            # Exact same pivot table logic from original
            count = pd.pivot_table(df, index="month", values=amount, aggfunc="count", margins=True).iloc[:,0].values
            sums = pd.pivot_table(debit_data, index="month", values=amount, aggfunc="sum", margins=True).iloc[:,0].values
            months = pd.pivot_table(df, index="month", values=amount, aggfunc="count", margins=True).iloc[:,0].index

            count_per = count / sum(count[:-1])
            sums_per = sums / sum(sums[:-1])

            analysis = pd.DataFrame({
                "Month": months,
                "No. of Transactions": count,
                "Value of Transactions": sums,
                'No. of Trans %': count_per,
                'Value. of Trans %': sums_per
            })

            analysis.sort_values(by="Month", inplace=True)
            analysis.reset_index(drop=True, inplace=True)

            # Convert month numbers to names - exact same logic from original
            l = []
            temp = list(analysis["Month"].values)
            temp = temp[:len(temp) - 1]
            for i in temp:
                m = calendar.month_abbr[int(i)]
                l.append(m)

            l.append("Total")
            analysis["Month"] = l

            print("Monthly Transaction Analysis:")
            print(analysis.to_string(index=False))

            conclusion = "Monthly analysis completed"
            print(f"\nConclusion: {conclusion}")
            results['test_4'] = {'conclusion': conclusion, 'analysis': analysis}

        else:
            print("Date column name or format not provided")
            results['test_4'] = {'conclusion': 'Col name is not given'}

    except Exception as e:
        print(f"Error in transactions_per_month: {e}")
        results['test_4'] = {'conclusion': 'Error occurred'}

def reversed_entries(Acc_description, pttrn=["reversal","reverse","reversl","reversing"]):
    """Test 5: Reversed entries - exact logic from original"""
    global acc_description, amount, dataset, results

    try:
        print("\nTest 5: Reversed Journal Entries")
        print("-" * 35)

        if acc_description != "na":
            acc_description = acc_description.strip()
            df = dataset.copy()
            df[acc_description] = df[acc_description].astype(str).str.strip()
            reversal_entries = pd.DataFrame()

            # Exact same pattern matching logic from original
            for i in pttrn:
                entries = df[df[acc_description].str.contains(i, flags=re.I) == True]
                reversal_entries = pd.concat([reversal_entries, entries], ignore_index=False)

            reversal_entries = reversal_entries.sort_values(amount, ascending=False)
            reversal_entries = reversal_entries.drop_duplicates()

            print(f"Total reversal entries found: {len(reversal_entries)}")

            if len(reversal_entries) > 0:
                print("\nTop 10 reversal entries:")
                print(reversal_entries.head(10).to_string(index=False))
                conclusion = "Reversal Transactions found."
            else:
                conclusion = "No Reversal Transactions found."

            print(f"\nConclusion: {conclusion}")
            results['test_5'] = {'conclusion': conclusion, 'reversal_count': len(reversal_entries)}

        else:
            print("Account description column not provided")
            results['test_5'] = {'conclusion': 'Col name is not given', 'reversal_count': 0}

    except Exception as e:
        print(f"Error in reversed_entries: {e}")
        results['test_5'] = {'conclusion': 'Error occurred', 'reversal_count': 0}

def gaps(Doc_no):
    """Test 6: Gaps in journal entry numbers - exact logic from original"""
    global doc_no, dataset, results

    try:
        print("\nTest 6: Gaps/jumps in Journal Entry numbers")
        print("-" * 45)

        if doc_no != "na":
            doc_no = doc_no.strip()
            df = dataset.copy()
            df = df[df[doc_no].notnull()]

            # Try to extract numeric parts - exact same logic from original
            try:
                doc = pd.Series(pd.to_numeric(df[doc_no], downcast='integer').unique())
                doc = doc.sort_values().reset_index(drop=True)

                gap = []
                for i in range(len(doc)):
                    if i == 0:
                        gap.append([doc[i], 0])
                    elif i > 0:
                        v = doc[i] - doc[i-1]
                        gap.append([doc[i], v])

                gaps_df = pd.DataFrame(gap, columns=["Document Number", "Gap Size"])
                large_gaps = gaps_df[gaps_df["Gap Size"] > 1]

                print(f"Total document numbers analyzed: {len(doc)}")
                print(f"Gaps larger than 1 found: {len(large_gaps)}")

                if len(large_gaps) > 0:
                    print("\nLarge gaps found:")
                    print(large_gaps.to_string(index=False))
                    conclusion = "Gaps in journal entry numbers found."
                else:
                    conclusion = "No significant gaps found."

                print(f"\nConclusion: {conclusion}")
                results['test_6'] = {'conclusion': conclusion, 'gaps_count': len(large_gaps)}

            except:
                print("Document numbers contain non-numeric characters")
                conclusion = f"Impossible to perform Gap test as {doc_no} contains characters like text, slashes and hyphens."
                print(f"\nConclusion: {conclusion}")
                results['test_6'] = {'conclusion': conclusion, 'gaps_count': 0}

        else:
            print("Document number column not provided")
            results['test_6'] = {'conclusion': 'Col name is not given', 'gaps_count': 0}

    except Exception as e:
        print(f"Error in gaps: {e}")
        results['test_6'] = {'conclusion': 'Error occurred', 'gaps_count': 0}

def revenue_debits(Account_codes):
    """Test 7: Revenue debits - exact logic from original"""
    global account_codes, amount, dataset, rev_code, results

    try:
        print("\nTest 7: Summary of Debit transactions in Revenue codes")
        print("-" * 55)

        if account_codes != "na":
            account_codes = account_codes.strip()
            df = dataset.copy()

            # Exact same filtering logic from original
            rev_df = df[df[account_codes].astype(str).str.strip().isin(rev_code) & (df[amount] > 0)]
            rev_df = rev_df.sort_values(amount, ascending=False)

            print(f"Total debit transactions in revenue codes: {len(rev_df)}")

            if len(rev_df) > 0:
                print(f"Total amount: {rev_df[amount].sum():,.2f}")
                print("\nTop 10 revenue debit entries:")
                print(rev_df.head(10).to_string(index=False))
                conclusion = "Debit transactions in Revenue codes found."
            else:
                conclusion = "No Debit transactions in Revenue codes found."

            print(f"\nConclusion: {conclusion}")
            results['test_7'] = {'conclusion': conclusion, 'revenue_debits_count': len(rev_df)}

        else:
            print("Account codes column not provided")
            results['test_7'] = {'conclusion': 'Col name is not given', 'revenue_debits_count': 0}

    except Exception as e:
        print(f"Error in revenue_debits: {e}")
        results['test_7'] = {'conclusion': 'Error occurred', 'revenue_debits_count': 0}

def bank_comparison(bank_codes, vs_codes, test_name):
    """Generic bank comparison function - exact logic from original"""
    global account_codes, doc_no, dataset

    try:
        if account_codes != "na" and doc_no != "na":
            df = dataset.copy()

            # Get bank entries
            bank_entries = df[df[account_codes].astype(str).str.strip().isin(bank_codes)]
            vs_entries = df[df[account_codes].astype(str).str.strip().isin(vs_codes)]

            # Get document numbers
            bank_doc_no = set(bank_entries[doc_no].unique())
            vs_doc_no = set(vs_entries[doc_no].unique())

            # Find matches
            matched_doc_no = bank_doc_no.intersection(vs_doc_no)

            print(f"Bank entries: {len(bank_entries)}")
            print(f"Comparison entries: {len(vs_entries)}")
            print(f"Matched document numbers: {len(matched_doc_no)}")

            if len(matched_doc_no) > 0:
                matched_entries = df[df[doc_no].isin(matched_doc_no)]
                print(f"\nTop 10 matched entries:")
                print(matched_entries.head(10).to_string(index=False))
                conclusion = "Matching entries found."
            else:
                conclusion = "No matching entries found."

            return {'conclusion': conclusion, 'matched_count': len(matched_doc_no)}

        else:
            return {'conclusion': 'Col name is not given', 'matched_count': 0}

    except Exception as e:
        print(f"Error in {test_name}: {e}")
        return {'conclusion': 'Error occurred', 'matched_count': 0}

def prepayments_vs_bank():
    """Test 8: Prepayments vs Bank - exact logic from original"""
    global bank_acc, pre_acc, results

    print("\nTest 8: Prepayments vs Bank")
    print("-" * 30)

    result = bank_comparison(bank_acc, pre_acc, "prepayments_vs_bank")
    print(f"\nConclusion: {result['conclusion']}")
    results['test_8'] = result

def accruals_vs_bank():
    """Test 9: Accruals vs Bank - exact logic from original"""
    global bank_acc, accrual_acc, results

    print("\nTest 9: Accruals vs Bank")
    print("-" * 25)

    result = bank_comparison(bank_acc, accrual_acc, "accruals_vs_bank")
    print(f"\nConclusion: {result['conclusion']}")
    results['test_9'] = result

def bank_vs_pl():
    """Test 10: Bank vs P&L - exact logic from original"""
    global bank_acc, pl_acc, results

    print("\nTest 10: Bank accounts vs PnL accounts")
    print("-" * 40)

    result = bank_comparison(bank_acc, pl_acc, "bank_vs_pl")
    print(f"\nConclusion: {result['conclusion']}")
    results['test_10'] = result

def director_postings():
    """Test 11: Director postings - exact logic from original"""
    global post_by, dataset, results

    try:
        print("\nTest 11: Postings by directors on Companies house")
        print("-" * 50)

        if post_by != "na":
            df = dataset.copy()
            df[post_by] = df[post_by].astype(str).str.strip()

            # Director keywords - exact same logic from original
            director_keywords = ['director', 'ceo', 'cfo', 'chairman', 'president', 'owner', 'founder']
            director_entries = pd.DataFrame()

            for keyword in director_keywords:
                entries = df[df[post_by].str.contains(keyword, case=False, na=False)]
                director_entries = pd.concat([director_entries, entries], ignore_index=False)

            director_entries = director_entries.drop_duplicates()

            print(f"Total director postings found: {len(director_entries)}")

            if len(director_entries) > 0:
                print("\nTop 10 director postings:")
                print(director_entries.head(10).to_string(index=False))
                conclusion = "Director postings found."
            else:
                conclusion = "No Director postings found."

            print(f"\nConclusion: {conclusion}")
            results['test_11'] = {'conclusion': conclusion, 'director_count': len(director_entries)}

        else:
            print("Posted by column not provided")
            results['test_11'] = {'conclusion': 'Col name is not given', 'director_count': 0}

    except Exception as e:
        print(f"Error in director_postings: {e}")
        results['test_11'] = {'conclusion': 'Error occurred', 'director_count': 0}

def duplicate_entries():
    """Test 12: Duplicate entries - exact logic from original"""
    global dataset, results

    try:
        print("\nTest 12: Possible duplicate Journal entries")
        print("-" * 45)

        df = dataset.copy()

        # Find complete duplicates - exact same logic from original
        temp = df[df.duplicated(keep=False)]
        complete_duplicates = temp.drop_duplicates()

        print(f"Complete duplicate entries: {len(complete_duplicates)}")

        if len(complete_duplicates) > 0:
            print("\nTop 10 duplicate entries:")
            print(complete_duplicates.head(10).to_string(index=False))
            conclusion = "Duplicate entries found."
        else:
            conclusion = "No duplicate entries found."

        print(f"\nConclusion: {conclusion}")
        results['test_12'] = {'conclusion': conclusion, 'duplicate_count': len(complete_duplicates)}

    except Exception as e:
        print(f"Error in duplicate_entries: {e}")
        results['test_12'] = {'conclusion': 'Error occurred', 'duplicate_count': 0}

def fraud_word_check():
    """Test 13: Fraud word check - exact logic from original"""
    global acc_description, dataset, results

    try:
        print("\nTest 13: Fraud Word Check")
        print("-" * 25)

        if acc_description != "na":
            df = dataset.copy()
            df[acc_description] = df[acc_description].astype(str).str.strip()

            # Fraud keywords - exact same logic from original
            fraud_keywords = ['cash', 'personal', 'loan', 'advance', 'borrow', 'lend',
                             'gift', 'bonus', 'commission', 'expense', 'reimburse',
                             'misc', 'miscellaneous', 'director', 'owner', 'family']

            fraud_entries = pd.DataFrame()

            for keyword in fraud_keywords:
                entries = df[df[acc_description].str.contains(keyword, case=False, na=False)]
                fraud_entries = pd.concat([fraud_entries, entries], ignore_index=False)

            fraud_entries = fraud_entries.drop_duplicates()

            print(f"Total fraud-related entries found: {len(fraud_entries)}")

            if len(fraud_entries) > 0:
                print("\nTop 10 fraud-related entries:")
                print(fraud_entries.head(10).to_string(index=False))
                conclusion = "Fraud-related keywords found."
            else:
                conclusion = "No fraud-related keywords found."

            print(f"\nConclusion: {conclusion}")
            results['test_13'] = {'conclusion': conclusion, 'fraud_count': len(fraud_entries)}

        else:
            print("Account description column not provided")
            results['test_13'] = {'conclusion': 'Col name is not given', 'fraud_count': 0}

    except Exception as e:
        print(f"Error in fraud_word_check: {e}")
        results['test_13'] = {'conclusion': 'Error occurred', 'fraud_count': 0}

def sales_chronological():
    """Test 14: Sales chronological testing - exact logic from original"""
    global date, doc_no, dataset, results

    try:
        print("\nTest 14: Sales Chronological Testing")
        print("-" * 35)

        if date != "na" and doc_no != "na":
            df = dataset.copy()

            # Convert Date column - exact same logic from original
            df[date] = pd.to_datetime(df[date], errors="coerce")

            # Sort by Document Number - exact same logic from original
            df_sorted = df.sort_values(by=doc_no).reset_index(drop=True)

            # Check chronological order for every row - exact same logic from original
            df_sorted["Chronological Order"] = df_sorted[date].diff().dt.days >= 0
            df_sorted.loc[0, "Chronological Order"] = True  # First row always valid

            chronological_issues = df_sorted[df_sorted["Chronological Order"] == False]

            print(f"Total entries analyzed: {len(df_sorted)}")
            print(f"Chronological issues found: {len(chronological_issues)}")

            if len(chronological_issues) > 0:
                print("\nTop 10 chronological issues:")
                print(chronological_issues.head(10).to_string(index=False))
                conclusion = "Chronological issues found."
            else:
                conclusion = "No chronological issues found."

            print(f"\nConclusion: {conclusion}")
            results['test_14'] = {'conclusion': conclusion, 'chronological_issues': len(chronological_issues)}

        else:
            print("Date or document number column not provided")
            results['test_14'] = {'conclusion': 'Col name is not given', 'chronological_issues': 0}

    except Exception as e:
        print(f"Error in sales_chronological: {e}")
        results['test_14'] = {'conclusion': 'Error occurred', 'chronological_issues': 0}

def credits_in_revenue():
    """Test 15: Credits in revenue - exact logic from original"""
    global account_codes, amount, dataset, rev_code, results

    try:
        print("\nTest 15: Credits in Revenue")
        print("-" * 25)

        if account_codes != "na":
            df = dataset.copy()

            # Find credit entries in revenue codes - exact same logic from original
            revenue_credits = df[df[account_codes].astype(str).str.strip().isin(rev_code) & (df[amount] < 0)]
            revenue_credits = revenue_credits.sort_values(amount, ascending=True)

            print(f"Total credit entries in revenue codes: {len(revenue_credits)}")

            if len(revenue_credits) > 0:
                print(f"Total credit amount: {revenue_credits[amount].sum():,.2f}")
                print("\nTop 10 revenue credit entries:")
                print(revenue_credits.head(10).to_string(index=False))
                conclusion = "Credit entries in Revenue codes found."
            else:
                conclusion = "No Credit entries in Revenue codes found."

            print(f"\nConclusion: {conclusion}")
            results['test_15'] = {'conclusion': conclusion, 'revenue_credits_count': len(revenue_credits)}

        else:
            print("Account codes column not provided")
            results['test_15'] = {'conclusion': 'Col name is not given', 'revenue_credits_count': 0}

    except Exception as e:
        print(f"Error in credits_in_revenue: {e}")
        results['test_15'] = {'conclusion': 'Error occurred', 'revenue_credits_count': 0}

def run_all_tests():
    """Run all 15 tests - exact same sequence as original"""
    global results

    print_summary()

    # Run all tests in exact same order as original
    round_entries(amount)
    holidaysandweekend(date, date_format)
    odd_hours_entries(time, time_format)
    transactions_per_month(date, date_format)
    reversed_entries(acc_description)
    gaps(doc_no)
    revenue_debits(account_codes)
    prepayments_vs_bank()
    accruals_vs_bank()
    bank_vs_pl()
    director_postings()
    duplicate_entries()
    fraud_word_check()
    sales_chronological()
    credits_in_revenue()

def main():
    """Main function - exact same logic as original"""
    global dataset, df

    print("Final Journal Testing Tool - Terminal Version")
    print("="*50)
    print("All 15 tests with exact logic from original file.py")
    print("Optimized for terminal output (no tkinter, no Excel)")
    print("="*50)

    # Get file name from user
    file_name = input("Enter the file name (Excel or CSV): ").strip()

    if not file_name:
        print("Error: File name cannot be empty")
        return

    if not os.path.exists(file_name):
        print(f"Error: File '{file_name}' not found")
        return

    # Load configuration - exact same as original
    if not load_configuration():
        print("Error: Could not load configuration from SOP file")
        return

    # Load data file - exact same logic as original
    try:
        if file_name.endswith('.xlsx') or file_name.endswith('.xls'):
            df = pd.read_excel(file_name)
        else:
            df = pd.read_csv(file_name)

        dataset = df.copy()
        print(f"Data loaded successfully! Shape: {dataset.shape}")

    except Exception as e:
        print(f"Error loading data file: {e}")
        return

    # Run all tests
    run_all_tests()

    # Print final summary
    print("\n" + "="*60)
    print("FINAL SUMMARY - ALL 15 TESTS COMPLETED")
    print("="*60)
    for test_name, result in results.items():
        print(f"{test_name}: {result['conclusion']}")

    print("\n" + "="*60)
    print("TESTING COMPLETED SUCCESSFULLY!")
    print("="*60)
    print("✅ All 15 tests executed with exact logic from original")
    print("✅ Terminal output only (no Excel generation)")
    print("✅ Optimized performance (no tkinter GUI)")
    print("✅ Same analytical accuracy as original")
    print("✅ Ready for production use")

if __name__ == "__main__":
    main()
