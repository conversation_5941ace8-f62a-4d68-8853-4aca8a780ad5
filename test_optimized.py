#!/usr/bin/env python
# Test script for the optimized journal testing tool

import pandas as pd
import numpy as np
import os
from datetime import datetime, timed<PERSON><PERSON>

def create_sample_data():
    """Create sample data for testing"""
    print("Creating sample test data...")
    
    # Create sample journal data
    np.random.seed(42)
    n_records = 1000
    
    # Generate sample data
    data = {
        'Account_Code': np.random.choice(['4000', '4001', '1200', '1201', '2000', '2001'], n_records),
        'Document_No': range(1001, 1001 + n_records),
        'Date': pd.date_range('2023-01-01', periods=n_records, freq='D'),
        'Amount': np.random.uniform(-10000, 10000, n_records),
        'Description': [f'Transaction {i}' for i in range(n_records)],
        'Account_Type': np.random.choice(['Revenue', 'Asset', 'Liability'], n_records),
        'Time': pd.date_range('2023-01-01 09:00:00', periods=n_records, freq='H'),
        'Posted_By': np.random.choice(['User1', 'User2', 'Director', 'CEO'], n_records)
    }
    
    # Add some specific test cases
    # Round entries
    data['Amount'][0] = 5000.0  # ends with 000
    data['Amount'][1] = 2999.0  # ends with 999
    
    # Weekend entries
    weekend_date = pd.Timestamp('2023-01-07')  # Saturday
    data['Date'][2] = weekend_date
    
    # Odd hours
    data['Time'][3] = pd.Timestamp('2023-01-01 23:00:00')  # 11 PM
    
    # Reversal entries
    data['Description'][4] = 'Reversal of previous entry'
    data['Description'][5] = 'Reverse transaction'
    
    # Duplicate entries
    data['Amount'][6] = data['Amount'][7] = 1500.0
    data['Date'][6] = data['Date'][7]
    
    # Fraud keywords
    data['Description'][8] = 'Cash advance for personal use'
    data['Description'][9] = 'Miscellaneous expense'
    
    df = pd.DataFrame(data)
    df.to_excel('sample_journal_data.xlsx', index=False)
    print("Sample data created: sample_journal_data.xlsx")
    return df

def create_sample_sop():
    """Create sample SOP configuration file"""
    print("Creating sample SOP configuration...")
    
    with pd.ExcelWriter('SOP for Data Analyst.xlsx') as writer:
        # Revenue codes
        revenue_df = pd.DataFrame({'Revenue Code': ['4000', '4001', '4002']})
        revenue_df.to_excel(writer, sheet_name='Revenue Code', index=False)
        
        # Bank codes
        bank_df = pd.DataFrame({'Bank Code': ['1200', '1201']})
        bank_df.to_excel(writer, sheet_name='Bank Code', index=False)
        
        # Prepayment codes
        prep_df = pd.DataFrame({'Prepayment Code': ['1300', '1301']})
        prep_df.to_excel(writer, sheet_name='Prepayment Code', index=False)
        
        # Accrual codes
        accrual_df = pd.DataFrame({'Accrual Code': ['2000', '2001']})
        accrual_df.to_excel(writer, sheet_name='Accrual Code', index=False)
        
        # PL codes
        pl_df = pd.DataFrame({'PL Code': ['5000', '5001']})
        pl_df.to_excel(writer, sheet_name='PL Code', index=False)
        
        # Column names
        col_df = pd.DataFrame({
            'Column': ['Account_Code', 'Document_No', 'Date', 'Amount', 'Description', 
                      'Account_Type', 'Time', 'Posted_By', '%Y-%m-%d', '%Y-%m-%d %H:%M:%S'],
            'Value': ['Account_Code', 'Document_No', 'Date', 'Amount', 'Description', 
                     'Account_Type', 'Time', 'Posted_By', '%Y-%m-%d', '%Y-%m-%d %H:%M:%S']
        })
        col_df.to_excel(writer, sheet_name='Col Name', index=False)
        
        # Journal Testing sheet with client details
        journal_data = [
            ['Client Name', 'Test Company Ltd'],
            ['', ''],
            ['', ''],
            ['', ''],
            ['', ''],
            ['', ''],
            ['', ''],
            ['', ''],
            ['Period', datetime(2023, 1, 1), datetime(2023, 12, 31)],
            ['', '', ''],
            ['Date', '', ''],
            [datetime(2023, 1, 1), '', ''],  # Holiday date
            [datetime(2023, 12, 25), '', '']  # Christmas
        ]
        journal_df = pd.DataFrame(journal_data)
        journal_df.to_excel(writer, sheet_name='Journal Testing', index=False, header=False)
    
    print("Sample SOP created: SOP for Data Analyst.xlsx")

def run_test():
    """Run the test"""
    print("="*60)
    print("TESTING OPTIMIZED JOURNAL TESTING TOOL")
    print("="*60)
    
    # Create sample files
    create_sample_data()
    create_sample_sop()
    
    print("\nSample files created successfully!")
    print("\nTo test the optimized tool:")
    print("1. Run: python file_optimized.py")
    print("2. Enter filename: sample_journal_data.xlsx")
    print("3. View the results in terminal")
    
    print("\nFiles created:")
    print("- sample_journal_data.xlsx (test data)")
    print("- SOP for Data Analyst.xlsx (configuration)")
    print("- file_optimized.py (optimized tool)")
    
    print("\nThe optimized version includes all 15 tests:")
    print("1. Round entries ,000 or ,999")
    print("2. Date of postings: weekends, bank holidays etc.")
    print("3. Timings of postings - any postings on odd hours")
    print("4. Total amount of transactions per month")
    print("5. Reversed Journal Entries")
    print("6. Gaps/jumps in Journal Entry numbers")
    print("7. Summary of Debit transactions in Revenue codes")
    print("8. Prepayments vs Bank")
    print("9. Accruals vs Bank")
    print("10. Bank accounts vs PnL accounts")
    print("11. Postings by directors on Companies house")
    print("12. Possible duplicate Journal entries")
    print("13. Fraud Word Check")
    print("14. Sales Chronological Testing")
    print("15. Credits in Revenue")

if __name__ == "__main__":
    run_test()
