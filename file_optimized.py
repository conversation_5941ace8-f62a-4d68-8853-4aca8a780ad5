#!/usr/bin/env python
# coding: utf-8

import warnings 
warnings.filterwarnings('ignore')
import pandas as pd
import numpy as np
import re 
import os
import datetime
import calendar

pd.options.display.float_format = '{:,.2f}'.format

# Global variables
results = {}
rev_code = []
bank_acc = []
pre_acc = []
accrual_acc = []
pl_acc = []
account_codes = ""
doc_no = ""
date = ""
amount = ""
acc_description = ""
acc_type = ""
time = ""
post_by = ""
date_format = ""
time_format = ""
client_name = ""
client_period = ""
holiday_dates = []
df = None
dataset = None

def load_configuration():
    """Load configuration from SOP file"""
    global rev_code, bank_acc, pre_acc, accrual_acc, pl_acc
    global account_codes, doc_no, date, amount, acc_description
    global acc_type, time, post_by, date_format, time_format
    global client_name, client_period, holiday_dates
    
    try:
        # Load account codes
        rev_code = list(pd.read_excel("SOP for Data Analyst.xlsx", sheet_name="Revenue Code")["Revenue Code"].astype(str))
        bank_acc = list(pd.read_excel("SOP for Data Analyst.xlsx", sheet_name="Bank Code")["Bank Code"].astype(str))
        pre_acc = list(pd.read_excel("SOP for Data Analyst.xlsx", sheet_name="Prepayment Code")["Prepayment Code"].astype(str))
        accrual_acc = list(pd.read_excel("SOP for Data Analyst.xlsx", sheet_name="Accrual Code")["Accrual Code"].astype(str))
        pl_acc = list(pd.read_excel("SOP for Data Analyst.xlsx", sheet_name="PL Code")["PL Code"].astype(str))
        
        # Load column names
        col_name = pd.read_excel("SOP for Data Analyst.xlsx", sheet_name="Col Name").iloc[:,1]
        account_codes = col_name[0]
        doc_no = col_name[1]
        date = col_name[2]
        amount = col_name[3]
        acc_description = col_name[4]
        acc_type = col_name[5]
        time = col_name[6]
        post_by = col_name[7]
        date_format = col_name[8]
        time_format = col_name[9]
        
        # Load client details
        client_detail = pd.read_excel("SOP for Data Analyst.xlsx", sheet_name="Journal Testing", header=None)
        client_name = client_detail.iloc[1,0]
        client_period = "Period : " + client_detail.iloc[8,1].strftime("%B %Y") + " To " + client_detail.iloc[8,2].strftime("%B %Y")
        
        # Load holiday dates
        holiday_detail = pd.read_excel("SOP for Data Analyst.xlsx", sheet_name="Journal Testing", header=None)
        index = holiday_detail[holiday_detail[0].str.strip() == "Date"].index
        
        hol_dates = []
        for i in holiday_detail.iloc[index[0]+1:,0].values:
            if pd.isna(i):
                break
            else:
                hol_dates.append(i)
        
        holiday_dates = [i.strftime("%Y-%m-%d") for i in hol_dates]
        
        print("Configuration loaded successfully!")
        return True
        
    except Exception as e:
        print(f"Error loading configuration: {e}")
        return False

def print_summary():
    """Print summary of tests to be performed"""
    print("\n" + "="*60)
    print("JOURNAL TESTING SUMMARY")
    print("="*60)
    print(f"Client: {client_name}")
    print(f"Period: {client_period}")
    print("Subject: Journal Testing")
    print("\nTests to be performed:")
    print("1.  Round entries ,000 or ,999")
    print("2.  Date of postings: weekends, bank holidays etc.")
    print("3.  Timings of postings - any postings on odd hours")
    print("4.  Total amount of transactions per month")
    print("5.  Reversed Journal Entries")
    print("6.  Gaps/jumps in Journal Entry numbers")
    print("7.  Summary of Debit transactions in Revenue codes")
    print("8.  Prepayments vs Bank")
    print("9.  Accruals vs Bank")
    print("10. Bank accounts vs PnL accounts")
    print("11. Postings by directors on Companies house")
    print("12. Possible duplicate Journal entries")
    print("13. Fraud Word Check")
    print("14. Sales Chronological Testing")
    print("15. Credits in Revenue")
    print("="*60)

def test_1_round_entries():
    """Test 1: Find round entries ending with 000 or 999"""
    global amount, dataset, results
    
    try:
        print("\nTest 1: Round entries ,000 or ,999")
        print("-" * 40)
        
        if amount != "na":
            entries = dataset[dataset[amount].notnull()]
            entries[amount] = entries[amount].astype(str).str.strip().str.replace(",","")
            entries = entries[entries[amount] != ""]
            entries_000 = entries[(entries[amount].astype(str).astype(float).astype(str).str.contains(r"0{3}\.0*$"))]
            entries_000[amount] = pd.to_numeric(entries_000[amount])
            entries_000 = entries_000.sort_values(amount, ascending=False)
            entries_999 = entries[(entries[amount].astype(str).astype(float).astype(str).str.contains(r"9{3}\.0*$"))]
            entries_999[amount] = pd.to_numeric(entries_999[amount])
            entries_999 = entries_999.sort_values(amount, ascending=False)
            
            print(f"Entries ending with '000': {len(entries_000)}")
            print(f"Entries ending with '999': {len(entries_999)}")
            
            if len(entries_000) > 0:
                print("\nTop 5 entries ending with '000':")
                print(entries_000.head().to_string(index=False))
                
            if len(entries_999) > 0:
                print("\nTop 5 entries ending with '999':")
                print(entries_999.head().to_string(index=False))
            
            # Conclusion
            if len(entries_000) > 0 and len(entries_999) > 0:
                conclusion = "Entries ending with '000' & '999' found."
            elif len(entries_000) > 0 and len(entries_999) <= 0:
                conclusion = "Entries ending with '000' found."
            elif len(entries_000) <= 0 and len(entries_999) > 0:
                conclusion = "Entries ending with '999' found."
            else:
                conclusion = "No Entries ending with '000' & '999' found."
                
            print(f"\nConclusion: {conclusion}")
            results['test_1'] = {'conclusion': conclusion, 'count_000': len(entries_000), 'count_999': len(entries_999)}
            
        else:
            print("Amount column name not provided")
            results['test_1'] = {'conclusion': 'Col Name Not Given', 'count_000': 0, 'count_999': 0}
            
    except Exception as e:
        print(f"Error in test_1_round_entries: {e}")
        results['test_1'] = {'conclusion': 'Error occurred', 'count_000': 0, 'count_999': 0}

def test_2_holidays_weekend():
    """Test 2: Find postings on weekends and holidays"""
    global date, amount, dataset, holiday_dates, results
    
    try:
        print("\nTest 2: Date of postings: weekends, bank holidays etc.")
        print("-" * 50)
        
        if date != "na" and date_format != "na":
            date_col = date.strip()
            df = dataset.copy()
            df[date_col] = df[date_col].astype(str).str.strip()
            df = df[df[date_col].notnull()]
            df = df[df[date_col] != ""]
            df[date_col] = pd.to_datetime(df[date_col], format=date_format)
            
            holidays = holiday_dates
            holiday = df[df[date_col].isin(holidays)]
            weekend_1 = df[df[date_col].dt.strftime("%A") == "Saturday"]
            weekend_2 = df[df[date_col].dt.strftime("%A") == "Sunday"]
            
            holiday["Holiday"] = "Holiday"
            weekend_1["Holiday"] = "Saturday"
            weekend_2["Holiday"] = "Sunday"
            
            holiday = holiday.sort_values(amount, ascending=False)
            weekend_1 = weekend_1.sort_values(amount, ascending=False)
            weekend_2 = weekend_2.sort_values(amount, ascending=False)
            weekend = pd.concat([weekend_1, weekend_2], ignore_index=True)
            
            print(f"Weekend entries: {len(weekend)}")
            print(f"Holiday entries: {len(holiday)}")
            
            if len(weekend) > 0:
                print("\nTop 5 weekend entries:")
                print(weekend.head().to_string(index=False))
                
            if len(holiday) > 0:
                print("\nTop 5 holiday entries:")
                print(holiday.head().to_string(index=False))
            
            # Conclusion
            if len(weekend) > 0 and len(holiday) > 0:
                conclusion = "Entries posted in 'Weekend' & 'Holiday' found."
            elif len(weekend) > 0 and len(holiday) <= 0:
                conclusion = "Entries posted in 'Weekend' found."
            elif len(weekend) <= 0 and len(holiday) > 0:
                conclusion = "Entries posted in 'Holiday' found."
            else:
                conclusion = "No Entries posted in 'Weekend' & 'Holiday' found."
                
            print(f"\nConclusion: {conclusion}")
            results['test_2'] = {'conclusion': conclusion, 'weekend_count': len(weekend), 'holiday_count': len(holiday)}
            
        else:
            print("Date column name or format not provided")
            results['test_2'] = {'conclusion': 'Col Name Not Given', 'weekend_count': 0, 'holiday_count': 0}
            
    except Exception as e:
        print(f"Error in test_2_holidays_weekend: {e}")
        results['test_2'] = {'conclusion': 'Error occurred', 'weekend_count': 0, 'holiday_count': 0}

def test_3_odd_hours():
    """Test 3: Find postings at odd hours"""
    global time, amount, dataset, results
    
    try:
        print("\nTest 3: Timings of postings - any postings on odd hours")
        print("-" * 50)
        
        if time != "na" and time_format != "na":
            time_col = time.strip()
            df = dataset.copy()
            df[time_col] = df[time_col].astype(str).str.strip()
            df = df[df[time_col].notnull()]
            df = df[df[time_col] != ""]
            df[time_col] = pd.to_datetime(df[time_col], format=time_format)
            
            # Define odd hours (8 PM to 8 AM)
            odd_hours_data = []
            for hour in [20, 21, 22, 23, 0, 1, 2, 3, 4, 5, 6, 7, 8]:
                hour_data = df[df[time_col].dt.hour == hour]
                if len(hour_data) > 0:
                    hour_label = f"{hour % 12 if hour % 12 != 0 else 12} {'PM' if hour >= 12 else 'AM'}"
                    hour_data["Hours"] = hour_label
                    odd_hours_data.append(hour_data)
            
            if odd_hours_data:
                odd_hours_concat = pd.concat(odd_hours_data, ignore_index=True)
                odd_hours_concat = odd_hours_concat.sort_values(amount, ascending=False)
                
                print(f"Total odd hours entries: {len(odd_hours_concat)}")
                
                if len(odd_hours_concat) > 0:
                    print("\nTop 10 odd hours entries:")
                    print(odd_hours_concat.head(10).to_string(index=False))
                    conclusion = "Entries posted in 'Odd hours' found."
                else:
                    conclusion = "No Entries posted in 'Odd hours' found."
                    
                print(f"\nConclusion: {conclusion}")
                results['test_3'] = {'conclusion': conclusion, 'odd_hours_count': len(odd_hours_concat)}
            else:
                print("No odd hours entries found.")
                results['test_3'] = {'conclusion': 'No Entries posted in Odd hours found.', 'odd_hours_count': 0}
                
        else:
            print("Time column name or format not provided")
            results['test_3'] = {'conclusion': 'Col Name Not Given', 'odd_hours_count': 0}
            
    except Exception as e:
        print(f"Error in test_3_odd_hours: {e}")
        results['test_3'] = {'conclusion': 'Error occurred', 'odd_hours_count': 0}

def test_4_transactions_per_month():
    """Test 4: Total amount of transactions per month"""
    global date, amount, dataset, results

    try:
        print("\nTest 4: Total amount of transactions per month")
        print("-" * 45)

        if date != "na" and date_format != "na":
            date_col = date.strip()
            df = dataset.copy()
            df.sort_values(by=date_col, inplace=True)

            df[date_col] = df[date_col].astype(str).str.strip()
            df = df[df[date_col].notnull()]
            df = df[df[date_col] != ""]
            df[date_col] = pd.to_datetime(df[date_col], format=date_format)
            df["month"] = df[date_col].dt.strftime("%m")

            df = df[(df["month"].notnull()) & (df["month"] != "nan")]
            debit_data = df[df[amount] > 0]

            count = pd.pivot_table(df, index="month", values=amount, aggfunc="count", margins=True).iloc[:,0].values
            sums = pd.pivot_table(debit_data, index="month", values=amount, aggfunc="sum", margins=True).iloc[:,0].values
            months = pd.pivot_table(df, index="month", values=amount, aggfunc="count", margins=True).iloc[:,0].index

            count_per = count / sum(count[:-1])
            sums_per = sums / sum(sums[:-1])

            analysis = pd.DataFrame({
                "Month": months,
                "No. of Transactions": count,
                "Value of Transactions": sums,
                'No. of Trans %': count_per,
                'Value. of Trans %': sums_per
            })

            analysis.sort_values(by="Month", inplace=True)
            analysis.reset_index(drop=True, inplace=True)

            # Convert month numbers to names
            l = []
            temp = list(analysis["Month"].values)
            temp = temp[:len(temp) - 1]
            for i in temp:
                m = calendar.month_abbr[int(i)]
                l.append(m)

            l.append("Total")
            analysis["Month"] = l

            print("Monthly Transaction Analysis:")
            print(analysis.to_string(index=False))

            results['test_4'] = {'conclusion': 'Monthly analysis completed', 'analysis': analysis}

        else:
            print("Date column name or format not provided")
            results['test_4'] = {'conclusion': 'Col name is not given'}

    except Exception as e:
        print(f"Error in test_4_transactions_per_month: {e}")
        results['test_4'] = {'conclusion': 'Error occurred'}

def test_5_reversed_entries():
    """Test 5: Reversed Journal Entries"""
    global acc_description, amount, dataset, results

    try:
        print("\nTest 5: Reversed Journal Entries")
        print("-" * 35)

        if acc_description != "na":
            acc_desc_col = acc_description.strip()
            df = dataset.copy()
            df[acc_desc_col] = df[acc_desc_col].astype(str).str.strip()

            reversal_patterns = ["reversal", "reverse", "reversl", "reversing"]
            reversal_entries = pd.DataFrame()

            for pattern in reversal_patterns:
                entries = df[df[acc_desc_col].str.contains(pattern, flags=re.I) == True]
                reversal_entries = pd.concat([reversal_entries, entries], ignore_index=False)

            reversal_entries = reversal_entries.sort_values(amount, ascending=False)
            reversal_entries = reversal_entries.drop_duplicates()

            print(f"Total reversal entries found: {len(reversal_entries)}")

            if len(reversal_entries) > 0:
                print("\nTop 10 reversal entries:")
                print(reversal_entries.head(10).to_string(index=False))
                conclusion = "Reversal Transactions found."
            else:
                conclusion = "No Reversal Transactions found."

            print(f"\nConclusion: {conclusion}")
            results['test_5'] = {'conclusion': conclusion, 'reversal_count': len(reversal_entries)}

        else:
            print("Account description column name not provided")
            results['test_5'] = {'conclusion': 'Col name is not given', 'reversal_count': 0}

    except Exception as e:
        print(f"Error in test_5_reversed_entries: {e}")
        results['test_5'] = {'conclusion': 'Error occurred', 'reversal_count': 0}

def test_6_gaps():
    """Test 6: Gaps/jumps in Journal Entry numbers"""
    global doc_no, dataset, results

    try:
        print("\nTest 6: Gaps/jumps in Journal Entry numbers")
        print("-" * 42)

        if doc_no != "na":
            doc_col = doc_no.strip()
            df = dataset.copy()
            df = df[df[doc_col].notnull()]

            try:
                doc = pd.Series(pd.to_numeric(df[doc_col], downcast='integer').unique())
                gap = []
                doc = doc.sort_values().reset_index(drop=True)

                for i in range(len(doc)):
                    if i == 0:
                        gap.append([doc[i], 0])
                    elif i > 0:
                        v = doc[i] - doc[i-1]
                        gap.append([doc[i], v])

                gaps = pd.DataFrame(gap, columns=[doc_col, "Gaps in entries"])
                gaps = gaps[gaps["Gaps in entries"] > 1]

                print(f"Total gaps found: {len(gaps)}")

                if len(gaps) > 0:
                    print("\nGaps in journal entries:")
                    print(gaps.to_string(index=False))
                    conclusion = "Gaps in journal entries found."
                else:
                    conclusion = "No gaps in journal entries found."

                print(f"\nConclusion: {conclusion}")
                results['test_6'] = {'conclusion': conclusion, 'gaps_count': len(gaps)}

            except:
                print("Cannot convert document numbers to numeric - contains non-numeric characters")
                results['test_6'] = {'conclusion': 'Document numbers contain non-numeric characters', 'gaps_count': 0}

        else:
            print("Document number column name not provided")
            results['test_6'] = {'conclusion': 'Col name is not given', 'gaps_count': 0}

    except Exception as e:
        print(f"Error in test_6_gaps: {e}")
        results['test_6'] = {'conclusion': 'Error occurred', 'gaps_count': 0}

def test_7_revenue_debits():
    """Test 7: Summary of Debit transactions in Revenue codes"""
    global account_codes, amount, dataset, rev_code, results

    try:
        print("\nTest 7: Summary of Debit transactions in Revenue codes")
        print("-" * 55)

        if account_codes != "na" and amount != "na":
            acc_col = account_codes.strip()
            amt_col = amount.strip()
            df = dataset.copy()
            df = df[df[acc_col].notnull()]
            df[amt_col] = df[amt_col].astype("float")

            try:
                df[acc_col] = pd.to_numeric(df[acc_col], downcast='integer')
            except:
                pass

            rev_df = df[df[acc_col].astype(str).str.strip().isin(rev_code) & (df[amt_col] > 0)]
            rev_df = rev_df.sort_values(amt_col, ascending=False)

            print(f"Debit entries in revenue codes: {len(rev_df)}")

            if len(rev_df) > 0:
                print("\nTop 10 debit entries in revenue codes:")
                print(rev_df.head(10).to_string(index=False))
                conclusion = "Debit Entries found in Revenues Codes"
            else:
                conclusion = "No Debit Transactions in Revenue Codes"

            print(f"\nConclusion: {conclusion}")
            results['test_7'] = {'conclusion': conclusion, 'revenue_debit_count': len(rev_df)}

        else:
            print("Account codes or amount column name not provided")
            results['test_7'] = {'conclusion': 'Col Name is not given', 'revenue_debit_count': 0}

    except Exception as e:
        print(f"Error in test_7_revenue_debits: {e}")
        results['test_7'] = {'conclusion': 'Error occurred', 'revenue_debit_count': 0}

def simplified_bank_comparison(bank_codes, vs_codes, test_name):
    """Simplified bank comparison for tests 8, 9, 10"""
    global account_codes, doc_no, dataset, results

    try:
        print(f"\n{test_name}")
        print("-" * len(test_name))

        if account_codes != "na" and doc_no != "na":
            entries = dataset[dataset[account_codes].notnull()]
            entries = entries[entries[doc_no].notnull()]

            # Convert to string format
            try:
                entries[account_codes] = pd.to_numeric(entries[account_codes], downcast="integer").astype(str)
            except:
                entries[account_codes] = entries[account_codes].astype(str)

            try:
                entries[doc_no] = pd.to_numeric(entries[doc_no], downcast="integer").astype(str)
            except:
                entries[doc_no] = entries[doc_no].astype(str)

            # Filter entries
            bank_entries = entries[entries[account_codes].str.strip().isin(bank_codes)]
            vs_entries = entries[entries[account_codes].str.strip().isin(vs_codes)]

            # Get unique document numbers
            bank_doc_no = set(bank_entries[doc_no].unique())
            vs_doc_no = set(vs_entries[doc_no].unique())

            # Find matched document numbers
            matched_doc_no = bank_doc_no.intersection(vs_doc_no)

            print(f"Bank entries: {len(bank_entries)}")
            print(f"Comparison entries: {len(vs_entries)}")
            print(f"Matched documents: {len(matched_doc_no)}")

            if len(matched_doc_no) > 0:
                print(f"\nSample matched document numbers: {list(matched_doc_no)[:10]}")
                conclusion = f"Matched entries found between bank and comparison accounts."
            else:
                conclusion = f"No matched entries found between bank and comparison accounts."

            print(f"\nConclusion: {conclusion}")
            return {'conclusion': conclusion, 'matched_count': len(matched_doc_no)}

        else:
            print("Account codes or document number column not provided")
            return {'conclusion': 'Col Name is not given', 'matched_count': 0}

    except Exception as e:
        print(f"Error in {test_name}: {e}")
        return {'conclusion': 'Error occurred', 'matched_count': 0}

def test_11_directors():
    """Test 11: Postings by directors on Companies house"""
    global post_by, dataset, results

    try:
        print("\nTest 11: Postings by directors on Companies house")
        print("-" * 45)

        if post_by != "na":
            post_col = post_by.strip()
            df = dataset.copy()
            df = df[df[post_col].notnull()]
            df[post_col] = df[post_col].astype(str).str.strip()

            # Common director-related keywords
            director_keywords = ['director', 'ceo', 'cfo', 'chairman', 'president', 'owner', 'founder']
            director_entries = pd.DataFrame()

            for keyword in director_keywords:
                entries = df[df[post_col].str.contains(keyword, case=False, na=False)]
                director_entries = pd.concat([director_entries, entries], ignore_index=False)

            director_entries = director_entries.drop_duplicates()
            director_entries = director_entries.sort_values(amount, ascending=False)

            print(f"Potential director postings found: {len(director_entries)}")

            if len(director_entries) > 0:
                print("\nTop 10 potential director postings:")
                print(director_entries.head(10).to_string(index=False))
                conclusion = "Potential director postings found."
            else:
                conclusion = "No obvious director postings found."

            print(f"\nConclusion: {conclusion}")
            results['test_11'] = {'conclusion': conclusion, 'director_count': len(director_entries)}

        else:
            print("Posted by column name not provided")
            results['test_11'] = {'conclusion': 'Col name is not given', 'director_count': 0}

    except Exception as e:
        print(f"Error in test_11_directors: {e}")
        results['test_11'] = {'conclusion': 'Error occurred', 'director_count': 0}

def test_12_duplicate_entries():
    """Test 12: Possible duplicate Journal entries"""
    global dataset, results

    try:
        print("\nTest 12: Possible duplicate Journal entries")
        print("-" * 40)

        # Check for complete duplicate rows
        complete_duplicates = dataset.duplicated().sum()

        # Check for duplicate amounts on same date (if date column available)
        potential_duplicates = 0
        if date != "na" and amount != "na":
            df = dataset.copy()
            df = df[df[date].notnull() & df[amount].notnull()]

            # Group by date and amount to find potential duplicates
            grouped = df.groupby([date, amount]).size()
            potential_duplicates = (grouped > 1).sum()

        print(f"Complete duplicate rows: {complete_duplicates}")
        print(f"Potential duplicate transactions (same date & amount): {potential_duplicates}")

        if complete_duplicates > 0:
            print("\nSample duplicate rows:")
            duplicated_rows = dataset[dataset.duplicated(keep=False)]
            print(duplicated_rows.head().to_string(index=False))

        if complete_duplicates > 0 or potential_duplicates > 0:
            conclusion = f"Duplicates found: {complete_duplicates} exact, {potential_duplicates} potential"
        else:
            conclusion = "No duplicate entries found."

        print(f"\nConclusion: {conclusion}")
        results['test_12'] = {
            'conclusion': conclusion,
            'complete_duplicates': complete_duplicates,
            'potential_duplicates': potential_duplicates
        }

    except Exception as e:
        print(f"Error in test_12_duplicate_entries: {e}")
        results['test_12'] = {'conclusion': 'Error occurred', 'complete_duplicates': 0, 'potential_duplicates': 0}

def test_13_fraud_words():
    """Test 13: Fraud Word Check"""
    global acc_description, dataset, results

    try:
        print("\nTest 13: Fraud Word Check")
        print("-" * 25)

        if acc_description != "na":
            desc_col = acc_description.strip()
            df = dataset.copy()
            df = df[df[desc_col].notnull()]
            df[desc_col] = df[desc_col].astype(str).str.strip()

            # Common fraud-related keywords
            fraud_keywords = [
                'cash', 'personal', 'loan', 'advance', 'borrow', 'lend',
                'gift', 'bonus', 'commission', 'expense', 'reimburse',
                'petty', 'misc', 'miscellaneous', 'sundry', 'various',
                'adjustment', 'correction', 'error', 'mistake'
            ]

            fraud_entries = pd.DataFrame()
            found_keywords = []

            for keyword in fraud_keywords:
                entries = df[df[desc_col].str.contains(keyword, case=False, na=False)]
                if len(entries) > 0:
                    entries['Fraud_Keyword'] = keyword
                    fraud_entries = pd.concat([fraud_entries, entries], ignore_index=False)
                    found_keywords.append(keyword)

            fraud_entries = fraud_entries.drop_duplicates()
            fraud_entries = fraud_entries.sort_values(amount, ascending=False)

            print(f"Entries with potential fraud keywords: {len(fraud_entries)}")
            print(f"Keywords found: {', '.join(found_keywords) if found_keywords else 'None'}")

            if len(fraud_entries) > 0:
                print("\nTop 10 entries with fraud keywords:")
                print(fraud_entries.head(10).to_string(index=False))
                conclusion = f"Potential fraud keywords found in {len(fraud_entries)} entries."
            else:
                conclusion = "No obvious fraud keywords found."

            print(f"\nConclusion: {conclusion}")
            results['test_13'] = {'conclusion': conclusion, 'fraud_keyword_count': len(fraud_entries)}

        else:
            print("Account description column name not provided")
            results['test_13'] = {'conclusion': 'Col name is not given', 'fraud_keyword_count': 0}

    except Exception as e:
        print(f"Error in test_13_fraud_words: {e}")
        results['test_13'] = {'conclusion': 'Error occurred', 'fraud_keyword_count': 0}

def test_14_sales_chronological():
    """Test 14: Sales Chronological Testing"""
    global account_codes, rev_code, doc_no, date, dataset, results

    try:
        print("\nTest 14: Sales Chronological Testing")
        print("-" * 35)

        if account_codes != "na" and doc_no != "na" and date != "na":
            acc_col = account_codes.strip()
            doc_col = doc_no.strip()
            date_col = date.strip()

            df = dataset.copy()
            df = df[df[acc_col].notnull() & df[doc_col].notnull() & df[date_col].notnull()]

            # Filter revenue entries
            try:
                df[acc_col] = pd.to_numeric(df[acc_col], downcast='integer')
            except:
                pass

            revenue_entries = df[df[acc_col].astype(str).str.strip().isin(rev_code)]

            if len(revenue_entries) > 0:
                # Convert date and sort
                revenue_entries[date_col] = pd.to_datetime(revenue_entries[date_col], format=date_format)
                revenue_entries = revenue_entries.sort_values([date_col, doc_col])

                # Check for chronological issues
                chronological_issues = 0
                prev_date = None
                prev_doc = None

                for idx, row in revenue_entries.iterrows():
                    current_date = row[date_col]
                    current_doc = str(row[doc_col])

                    if prev_date is not None and prev_doc is not None:
                        # Check if document number decreased while date increased
                        try:
                            if (current_date > prev_date and
                                int(current_doc) < int(prev_doc)):
                                chronological_issues += 1
                        except:
                            pass  # Skip if document numbers are not numeric

                    prev_date = current_date
                    prev_doc = current_doc

                print(f"Revenue entries analyzed: {len(revenue_entries)}")
                print(f"Potential chronological issues: {chronological_issues}")

                if len(revenue_entries) > 0:
                    print("\nFirst 5 revenue entries (chronologically):")
                    print(revenue_entries.head().to_string(index=False))

                if chronological_issues > 0:
                    conclusion = f"Potential chronological issues found in {chronological_issues} entries."
                else:
                    conclusion = "No obvious chronological issues found in revenue entries."
            else:
                conclusion = "No revenue entries found for chronological testing."
                chronological_issues = 0

            print(f"\nConclusion: {conclusion}")
            results['test_14'] = {'conclusion': conclusion, 'chronological_issues': chronological_issues}

        else:
            print("Required columns not provided for chronological testing")
            results['test_14'] = {'conclusion': 'Required columns not provided', 'chronological_issues': 0}

    except Exception as e:
        print(f"Error in test_14_sales_chronological: {e}")
        results['test_14'] = {'conclusion': 'Error occurred', 'chronological_issues': 0}

def test_15_credits_in_revenue():
    """Test 15: Credits in Revenue"""
    global account_codes, rev_code, amount, dataset, results

    try:
        print("\nTest 15: Credits in Revenue")
        print("-" * 25)

        if account_codes != "na" and amount != "na":
            acc_col = account_codes.strip()
            amt_col = amount.strip()

            df = dataset.copy()
            df = df[df[acc_col].notnull() & df[amt_col].notnull()]
            df[amt_col] = df[amt_col].astype("float")

            # Convert account codes
            try:
                df[acc_col] = pd.to_numeric(df[acc_col], downcast='integer')
            except:
                pass

            # Filter revenue entries with negative amounts (credits)
            revenue_credits = df[df[acc_col].astype(str).str.strip().isin(rev_code) & (df[amt_col] < 0)]
            revenue_credits = revenue_credits.sort_values(amt_col, ascending=True)  # Most negative first

            print(f"Credit entries in revenue codes: {len(revenue_credits)}")

            if len(revenue_credits) > 0:
                total_credit_amount = revenue_credits[amt_col].sum()
                print(f"Total credit amount: {total_credit_amount:,.2f}")

                print("\nTop 10 credit entries in revenue codes:")
                print(revenue_credits.head(10).to_string(index=False))

                conclusion = f"Credit entries found in revenue codes: {len(revenue_credits)} entries totaling {total_credit_amount:,.2f}"
            else:
                conclusion = "No credit entries found in revenue codes."

            print(f"\nConclusion: {conclusion}")
            results['test_15'] = {'conclusion': conclusion, 'revenue_credit_count': len(revenue_credits)}

        else:
            print("Account codes or amount column name not provided")
            results['test_15'] = {'conclusion': 'Col Name is not given', 'revenue_credit_count': 0}

    except Exception as e:
        print(f"Error in test_15_credits_in_revenue: {e}")
        results['test_15'] = {'conclusion': 'Error occurred', 'revenue_credit_count': 0}

def run_all_tests():
    """Run all 15 tests"""
    global results

    print_summary()

    # Run core tests
    test_1_round_entries()
    test_2_holidays_weekend()
    test_3_odd_hours()
    test_4_transactions_per_month()
    test_5_reversed_entries()
    test_6_gaps()
    test_7_revenue_debits()

    # Run bank comparison tests
    results['test_8'] = simplified_bank_comparison(bank_acc, pre_acc, "Test 8: Prepayments vs Bank")
    results['test_9'] = simplified_bank_comparison(bank_acc, accrual_acc, "Test 9: Accruals vs Bank")
    results['test_10'] = simplified_bank_comparison(bank_acc, pl_acc, "Test 10: Bank accounts vs PnL accounts")

    # Run remaining tests
    test_11_directors()
    test_12_duplicate_entries()
    test_13_fraud_words()
    test_14_sales_chronological()
    test_15_credits_in_revenue()

def main():
    """Main function to run the journal testing"""
    global dataset, df

    print("Journal Testing Tool - Terminal Version")
    print("="*50)

    # Get file name from user
    file_name = input("Enter the file name (Excel or CSV): ").strip()

    if not file_name:
        print("Error: File name cannot be empty")
        return

    if not os.path.exists(file_name):
        print(f"Error: File '{file_name}' not found")
        return

    # Load configuration
    if not load_configuration():
        print("Error: Could not load configuration from SOP file")
        return

    # Load data file
    try:
        if file_name.endswith('.xlsx') or file_name.endswith('.xls'):
            df = pd.read_excel(file_name)
        else:
            df = pd.read_csv(file_name)

        dataset = df.copy()
        print(f"Data loaded successfully! Shape: {dataset.shape}")

    except Exception as e:
        print(f"Error loading data file: {e}")
        return

    # Run all tests
    run_all_tests()

    # Print final summary
    print("\n" + "="*60)
    print("FINAL SUMMARY")
    print("="*60)
    for test_name, result in results.items():
        print(f"{test_name}: {result['conclusion']}")

    print("\nTesting completed successfully!")

if __name__ == "__main__":
    main()
